import React from 'react';
import { useNavigate } from 'react-router-dom';
import '../styles/AgriIntelLanding.css';

const AgriIntelLanding: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="landing-container">
      <header className="landing-header">
        <div className="logo">AgriIntel</div>
        <nav className="nav-links">
          <a href="#home">Home</a>
          <a href="#about">About Us</a>
          <a href="#pricing">Pricing</a>
          <a href="#services">Services</a>
          <a href="#contact">Contact</a>
        </nav>
      </header>

      <main>
        <section id="home" className="hero-section">
          <div className="hero-content">
            <h1>Revolutionizing Agriculture with Technology</h1>
            <p>Bridging the skills gap for farmers, veterinarians, and industry partners through data-driven insights.</p>
            <div className="cta-buttons">
              <button className="cta-button register" onClick={() => navigate('/register')}>Register</button>
              <button className="cta-button beta" onClick={() => navigate('/login/beta')}>Explore BETA</button>
              <button className="cta-button professional" onClick={() => navigate('/login/professional')}>Go Professional</button>
              <button className="cta-button enterprise" onClick={() => navigate('/login/enterprise')}>Choose Enterprise</button>
            </div>
          </div>
        </section>

        <section id="about" className="content-section">
          <h2>About Us & Our Mission</h2>
          <p>
            AgriIntel was founded by May and Caiphus, visionary leaders dedicated to solving critical challenges in global agriculture. Our mission is to introduce modern technological practices to the agricultural sector, empowering farmers, veterinarians, and industry partners with the tools they need to thrive in a changing world. We believe in leveraging data for precision agriculture, enhancing food security through technology, optimizing supply chains with predictive analytics, and promoting sustainable farming practices for a better future.
          </p>
        </section>

        <section id="services" className="content-section">
          <h2>Our Services</h2>
          <p>
            We provide a comprehensive suite of tools designed to bridge the skills gap in the agricultural industry. Our platform offers advanced analytics, predictive modeling, and task automation to help farmers make informed decisions, improve efficiency, and increase profitability. For veterinarians, we offer tools for enhanced diagnostics and herd health management. We are committed to providing continuous support and training to ensure our partners can fully leverage the power of AgriIntel.
          </p>
        </section>

        <section id="pricing" className="content-section">
            <h2>Pricing Plans</h2>
            <p>Choose the plan that's right for your operation.</p>
            {/* Pricing details will be fleshed out here */}
        </section>

        <section id="contact" className="content-section">
          <h2>Contact Us</h2>
          <p>Have questions? Reach out to our team.</p>
          <p><strong>Phone:</strong> **********</p>
        </section>
      </main>

      <footer className="landing-footer">
        <p>&copy; {new Date().getFullYear()} AgriIntel. All Rights Reserved.</p>
      </footer>
    </div>
  );
};

export default AgriIntelLanding;