import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Container,
  Grid,
  Typography,
  Button,
  Card,
  CardContent,
  Box,
  Chip,
  Rating,
  CircularProgress
} from '@mui/material';
import {
  Agriculture,
  Pets,
  LocalHospital,
  TrendingUp,
  PlayArrow,
  ArrowForward,
  CheckCircle,
  Star
} from '@mui/icons-material';
import '../styles/professional-landing.css';

const AgriIntelLanding: React.FC = () => {
  const navigate = useNavigate();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingPlan, setLoadingPlan] = useState<string | null>(null);

  // Livestock images for rotating background
  const livestockImages = [
    `${process.env.PUBLIC_URL}/images/animals/cattle-1.jpeg`,
    `${process.env.PUBLIC_URL}/images/animals/cattle-2.avif`,
    `${process.env.PUBLIC_URL}/images/modules/animals/cattle-3.jpeg`,
    `${process.env.PUBLIC_URL}/images/modules/animals/cattle-4.jpeg`
  ];

  // Rotate background images every 5 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prev) => (prev + 1) % livestockImages.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [livestockImages.length]);

  // Two-tier subscription structure (NO Enterprise)
  const subscriptionPlans = [
    {
      id: 'beta',
      name: 'BETA Access',
      price: 'Free',
      period: '30 days trial',
      description: 'Perfect for small-scale South African farmers',
      theme: 'beta',
      popular: false,
      features: [
        'Up to 50 animals (clearly limited)',
        'Basic health monitoring',
        'Simple financial tracking',
        'Resources & government programs access',
        'Email support'
      ],
      ctaText: 'Start Free Trial',
      route: '/login/beta'
    },
    {
      id: 'professional',
      name: 'Professional',
      price: 'R699',
      period: 'per month',
      description: 'For growing commercial farms',
      theme: 'professional',
      popular: true,
      features: [
        'Unlimited animals',
        'Advanced health analytics',
        'Full financial management',
        'Breeding optimization',
        'Marketplace access',
        'Priority support',
        'AI-powered insights',
        'Custom reports'
      ],
      ctaText: 'Go Professional',
      route: '/login/professional'
    }
  ];

  // Feature highlights
  const features = [
    {
      icon: <Pets className="feature-icon" />,
      title: 'Smart Animal Management',
      description: 'Track health, breeding, and performance with AI-powered insights'
    },
    {
      icon: <LocalHospital className="feature-icon" />,
      title: 'Health Monitoring',
      description: 'Proactive health alerts and veterinary management'
    },
    {
      icon: <TrendingUp className="feature-icon" />,
      title: 'Financial Analytics',
      description: 'Maximize profits with detailed financial tracking'
    },
    {
      icon: <Agriculture className="feature-icon" />,
      title: 'Feed Optimization',
      description: 'Optimize feed costs and nutrition for better growth'
    }
  ];

  const handlePlanSelect = async (planId: string) => {
    setLoadingPlan(planId);
    setIsLoading(true);

    await new Promise(resolve => setTimeout(resolve, 1500));

    const plan = subscriptionPlans.find(p => p.id === planId);
    if (plan) {
      navigate(plan.route);
    }

    setIsLoading(false);
    setLoadingPlan(null);
  };

  return (
    <div className="agriintel-landing">
      <div
        className="landing-background"
        style={{
          backgroundImage: `url(${livestockImages[currentImageIndex]})`
        }}
      />

      <motion.nav
        className="landing-navigation"
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
      >
        <Container maxWidth="xl">
          <Box className="nav-container">
            <div className="nav-brand">
              <Typography variant="h3" className="agriintel-logo">
                🌾 AgriIntel
              </Typography>
              <Chip
                label="Production Ready"
                className="nav-badge"
                size="small"
              />
            </div>

            <div className="nav-actions">
              <Button
                variant="outlined"
                className="nav-button nav-button-outline"
                onClick={() => navigate('/login')}
              >
                Sign In
              </Button>
              <Button
                variant="contained"
                className="nav-button nav-button-primary"
                onClick={() => handlePlanSelect('beta')}
                disabled={isLoading}
                startIcon={isLoading ? <CircularProgress size={16} /> : <PlayArrow />}
              >
                Start Free Trial
              </Button>
            </div>
          </Box>
        </Container>
      </motion.nav>

      {/* Hero Section */}
      <section className="hero-section">
        <Container maxWidth="xl">
          <Grid container spacing={6} alignItems="center">
            <Grid item xs={12} lg={6}>
              <motion.div
                initial={{ opacity: 0, x: -60 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 1, ease: "easeOut" }}
              >
                <Typography variant="h1" className="hero-title">
                  Transform Your
                  <span className="hero-title-highlight">
                    Livestock Farm
                  </span>
                  with AI Intelligence
                </Typography>

                <Typography variant="h4" className="hero-subtitle">
                  Join 15,000+ South African farmers who trust AgriIntel for smarter
                  livestock management and increased profitability.
                </Typography>

                <div className="hero-actions">
                  <Button
                    variant="contained"
                    size="large"
                    className="cta-primary"
                    onClick={() => handlePlanSelect('beta')}
                    disabled={isLoading}
                    startIcon={loadingPlan === 'beta' ? <CircularProgress size={20} /> : <PlayArrow />}
                  >
                    {loadingPlan === 'beta' ? 'Loading...' : 'Start Free Trial'}
                  </Button>
                  <Button
                    variant="outlined"
                    size="large"
                    className="cta-secondary"
                    onClick={() => handlePlanSelect('professional')}
                    disabled={isLoading}
                    endIcon={loadingPlan === 'professional' ? <CircularProgress size={20} /> : <ArrowForward />}
                  >
                    {loadingPlan === 'professional' ? 'Loading...' : 'Go Professional'}
                  </Button>
                </div>

                <div className="hero-rating">
                  <Rating value={5} readOnly size="large" />
                  <Typography variant="h6">
                    4.9/5 from 2,500+ verified farmers
                  </Typography>
                </div>
              </motion.div>
            </Grid>

            <Grid item xs={12} lg={6}>
              <motion.div
                initial={{ opacity: 0, x: 60 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 1, ease: "easeOut", delay: 0.3 }}
              >
                <div className="hero-dashboard-preview">
                  <img
                    src={`${process.env.PUBLIC_URL}/images/dashboard/main-dashboard.jpg`}
                    alt="AgriIntel Dashboard Preview"
                    className="hero-dashboard-image"
                  />
                  <div className="dashboard-overlay">
                    <div className="overlay-badge">
                      <Star />
                      <span>Live Dashboard</span>
                    </div>
                  </div>
                </div>
              </motion.div>
            </Grid>
          </Grid>
        </Container>
      </section>

      {/* Features Section */}
      <section className="features-section">
        <Container maxWidth="xl">
          <Typography variant="h2" className="section-title">
            Powerful Features for Modern Farmers
          </Typography>

          <Grid container spacing={4}>
            {features.map((feature, index) => (
              <Grid item xs={12} sm={6} lg={3} key={index}>
                <Card className="feature-card">
                  <CardContent>
                    <div className="feature-icon-container">
                      {feature.icon}
                    </div>
                    <Typography variant="h5">
                      {feature.title}
                    </Typography>
                    <Typography variant="body1">
                      {feature.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Container>
      </section>

      {/* Pricing Section - Two Tiers Only */}
      <section className="pricing-section">
        <Container maxWidth="xl">
          <Typography variant="h2" className="section-title">
            Choose Your Plan
          </Typography>
          <Typography variant="h6" className="section-subtitle" sx={{ mb: 4 }}>
            Enterprise solutions available on request for custom farm requirements
          </Typography>

          <Grid container spacing={4} justifyContent="center">
            {subscriptionPlans.map((plan, index) => (
              <Grid item xs={12} md={6} key={plan.id}>
                <Card
                  className={`pricing-card pricing-card-${plan.theme} ${plan.popular ? 'popular' : ''}`}
                  onClick={() => handlePlanSelect(plan.id)}
                >
                  {plan.popular && (
                    <Chip label="Most Popular" className="popular-badge" />
                  )}
                  <CardContent>
                    <Typography variant="h4">{plan.name}</Typography>
                    <Typography variant="body1">{plan.description}</Typography>
                    <div className="plan-price">
                      <Typography variant="h2">{plan.price}</Typography>
                      <Typography variant="h6">{plan.period}</Typography>
                    </div>
                    <div className="plan-features">
                      {plan.features.map((feature, idx) => (
                        <div key={idx} className="feature-item">
                          <CheckCircle />
                          <Typography variant="body1">{feature}</Typography>
                        </div>
                      ))}
                    </div>
                    <Button
                      variant="contained"
                      fullWidth
                      size="large"
                      disabled={isLoading}
                      startIcon={loadingPlan === plan.id ? <CircularProgress size={20} /> : undefined}
                    >
                      {loadingPlan === plan.id ? 'Loading...' : plan.ctaText}
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Container>
      </section>
    </div>
  );
};

export default AgriIntelLanding;