import React from 'react';
import { motion } from 'framer-motion';
import '../styles/professional-landing.css';

const ProfessionalLanding: React.FC = () => {
  return (
    <div className="professional-landing">
      <main>
        <section className="hero-section">
          <div className="hero-content">
            <motion.h1
              className="hero-title"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              Elevate Your Farm with Intelligent Livestock Management
            </motion.h1>
            <motion.p
              className="hero-subtitle"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              Harness the power of AI to drive productivity, profitability, and sustainability.
            </motion.p>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <a href="/register" className="btn btn-primary">Start Your Free Trial</a>
            </motion.div>
          </div>
        </section>

        <section className="pricing-section">
          <div className="container">
            <h2 className="section-title">Choose Your Plan</h2>
            <p className="section-subtitle">
              Start for free and scale as you grow. All plans come with our commitment to your success.
            </p>
            <div className="pricing-grid">
              {/* BETA Tier */}
              <motion.div className="pricing-card" whileHover={{ y: -10 }}>
                <h3 className="tier-name">BETA</h3>
                <p className="tier-price">Free</p>
                <a href="/register?tier=beta" className="btn btn-secondary">Get Started</a>
                <ul className="tier-features">
                  <li>All Basic Modules</li>
                  <li>Standard Analytics</li>
                  <li>Community Support</li>
                </ul>
                <p className="tier-description">The perfect starting point to experience the core functionalities of AgriIntel.</p>
              </motion.div>

              {/* Professional Tier */}
              <motion.div className="pricing-card professional" whileHover={{ y: -10 }}>
                <h3 className="tier-name">Professional</h3>
                <p className="tier-price">$49<span>/mo</span></p>
                <a href="/register?tier=professional" className="btn btn-primary">Upgrade Now</a>
                <ul className="tier-features">
                  <li>Everything in BETA</li>
                  <li>Advanced AI Insights</li>
                  <li>Priority Email Support</li>
                  <li>Exclusive Content Access</li>
                </ul>
                <p className="tier-description">Unlock powerful AI-driven analytics and premium features to maximize your farm's potential.</p>
              </motion.div>

              {/* Enterprise Tier */}
              <motion.div className="pricing-card" whileHover={{ y: -10 }}>
                <h3 className="tier-name">Enterprise</h3>
                <p className="tier-price">Contact Us</p>
                <a href="/contact" className="btn btn-secondary">Contact Sales</a>
                <ul className="tier-features">
                  <li>Everything in Professional</li>
                  <li>Dedicated Account Manager</li>
                  <li>Custom Integrations</li>
                  <li>On-site Training</li>
                </ul>
                <p className="tier-description">A tailored solution for large-scale operations with unique requirements.</p>
              </motion.div>
            </div>
          </div>
        </section>

        <section className="app-announcement">
          <div className="container">
            <div className="app-announcement-content">
              <span className="app-announcement-icon">📱</span>
              <p className="app-announcement-text">Mobile App Coming Soon! Manage your farm on the go.</p>
            </div>
          </div>
        </section>
      </main>
    </div>
  );
};

export default ProfessionalLanding;
