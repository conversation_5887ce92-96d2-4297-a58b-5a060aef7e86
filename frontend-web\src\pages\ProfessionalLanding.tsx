import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Container,
  Grid,
  Typography,
  Button,
  Card,
  CardContent,
  Box,
  Chip,
  Rating
} from '@mui/material';
import {
  Agriculture,
  Pets,
  LocalHospital,
  TrendingUp,
  PlayArrow,
  ArrowForward,
  CheckCircle
} from '@mui/icons-material';
import '../styles/professional-landing.css';

const ProfessionalLanding: React.FC = () => {
  const navigate = useNavigate();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // Livestock images for background rotation
  const livestockImages = [
    '/images/animals/cattle-1.jpeg',
    '/images/animals/cattle-2.avif',
    '/images/modules/animals/cattle-3.jpeg',
    '/images/modules/animals/cattle-4.jpeg'
  ];

  // Rotate background images
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prev) => (prev + 1) % livestockImages.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [livestockImages.length]);

  // Subscription plans
  const subscriptionPlans = [
    {
      id: 'beta',
      name: 'BETA Access',
      price: 'Free',
      period: '30 days trial',
      description: 'Perfect for small-scale farmers',
      color: '#22C55E',
      popular: false,
      features: [
        'Up to 50 animals',
        'Basic health monitoring',
        'Simple financial tracking',
        'Resources access',
        'Email support'
      ]
    },
    {
      id: 'professional',
      name: 'Professional',
      price: 'R699',
      period: 'per month',
      description: 'For growing commercial farms',
      color: '#3B82F6',
      popular: true,
      features: [
        'Unlimited animals',
        'Advanced health analytics',
        'Full financial management',
        'Breeding optimization',
        'Marketplace access',
        'Priority support'
      ]
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      price: 'R599',
      period: 'per month',
      description: 'For large-scale operations',
      color: '#A855F7',
      popular: false,
      features: [
        'Everything in Professional',
        'Multi-farm management',
        'API access',
        'Custom integrations',
        'White-label options',
        'Dedicated support'
      ]
    }
  ];

  // Feature highlights
  const features = [
    {
      icon: <Pets />,
      title: 'Smart Animal Management',
      description: 'Track health, breeding, and performance with AI-powered insights'
    },
    {
      icon: <LocalHospital />,
      title: 'Health Monitoring',
      description: 'Proactive health alerts and veterinary management'
    },
    {
      icon: <TrendingUp />,
      title: 'Financial Analytics',
      description: 'Maximize profits with detailed financial tracking and forecasting'
    },
    {
      icon: <Agriculture />,
      title: 'Feed Optimization',
      description: 'Optimize feed costs and nutrition for better growth'
    }
  ];

  const handlePlanSelect = async (planId: string) => {
    setIsLoading(true);

    // Simulate loading
    await new Promise(resolve => setTimeout(resolve, 1000));

    switch (planId) {
      case 'beta':
        navigate('/login/beta');
        break;
      case 'professional':
        navigate('/login/professional');
        break;
      case 'enterprise':
        navigate('/login/enterprise');
        break;
      default:
        navigate('/login');
    }

    setIsLoading(false);
  };

  return (
    <div className="professional-landing">
      {/* Dynamic Background */}
      <div
        className="background-image"
        style={{
          backgroundImage: `url(${livestockImages[currentImageIndex]})`
        }}
      />

      {/* Navigation */}
      <motion.nav
        className="landing-nav"
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.8 }}
      >
        <Container maxWidth="lg">
          <Box display="flex" justifyContent="space-between" alignItems="center" py={2}>
            <Typography variant="h4" className="logo">
              🌾 AgriIntel
            </Typography>
            <Box display="flex" gap={2}>
              <Button
                variant="outlined"
                onClick={() => navigate('/login')}
                className="nav-button"
              >
                Sign In
              </Button>
              <Button
                variant="contained"
                onClick={() => handlePlanSelect('beta')}
                className="nav-button-primary"
              >
                Start Free Trial
              </Button>
            </Box>
          </Box>
        </Container>
      </motion.nav>

      {/* Hero Section */}
      <Container maxWidth="lg" sx={{ pt: 15, pb: 8 }}>
        <Grid container spacing={6} alignItems="center">
          <Grid item xs={12} md={6}>
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <Typography
                variant="h1"
                className="hero-title"
                sx={{
                  fontSize: { xs: '3rem', md: '4.5rem' },
                  fontWeight: 800,
                  mb: 3,
                  lineHeight: 1.1
                }}
              >
                Transform Your
                <Box component="span" sx={{ color: '#FFD700', display: 'block' }}>
                  Livestock Farm
                </Box>
                with AI Intelligence
              </Typography>

              <Typography
                variant="h5"
                className="hero-subtitle"
                sx={{
                  mb: 4,
                  lineHeight: 1.6,
                  fontSize: { xs: '1.2rem', md: '1.5rem' }
                }}
              >
                Join 15,000+ South African farmers who trust AgriIntel for smarter
                livestock management, AI-powered health monitoring, and increased profitability.
              </Typography>

              <Box display="flex" gap={2} flexWrap="wrap">
                <Button
                  variant="contained"
                  size="large"
                  onClick={() => handlePlanSelect('beta')}
                  className="cta-primary"
                  disabled={isLoading}
                  startIcon={<PlayArrow />}
                >
                  Start Free Trial
                </Button>
                <Button
                  variant="outlined"
                  size="large"
                  onClick={() => handlePlanSelect('professional')}
                  className="cta-secondary"
                  disabled={isLoading}
                  endIcon={<ArrowForward />}
                >
                  Go Professional
                </Button>
              </Box>

              <Box mt={4} display="flex" alignItems="center" gap={2}>
                <Rating value={5} readOnly size="small" />
                <Typography variant="body2" color="rgba(255,255,255,0.8)">
                  4.9/5 from 2,500+ farmers
                </Typography>
              </Box>
            </motion.div>
          </Grid>

          <Grid item xs={12} md={6}>
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <Box className="hero-image-container">
                <img
                  src="/images/dashboard/main-dashboard.jpg"
                  alt="AgriIntel Dashboard"
                  className="hero-image"
                />
              </Box>
            </motion.div>
          </Grid>
        </Grid>
      </Container>
    </div>
  );
};

export default ProfessionalLanding;
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  Box,
  Container,
  Grid,
  Card,
  CardContent,
  CardActions,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Tab,
  Tabs,
  Paper,
  IconButton,
  Fab
} from '@mui/material';
import {
  Phone,
  WhatsApp,
  Email,
  Star,
  CheckCircle,
  TrendingUp,
  Agriculture,
  Security,
  Analytics,
  Close
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import AgriIntelBrand from '../components/branding/AgriIntelBrand';
import '../styles/professional-landing.css';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`nav-tabpanel-${index}`}
      aria-labelledby={`nav-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const ProfessionalLanding: React.FC = () => {
  const navigate = useNavigate();
  const [currentTab, setCurrentTab] = useState(0);
  const [inquiryOpen, setInquiryOpen] = useState(false);
  const [inquiryForm, setInquiryForm] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    message: '',
    tier: 'Enterprise'
  });

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  const handleInquirySubmit = () => {
    // Create WhatsApp message
    const message = `AgriIntel Enterprise Inquiry:
Name: ${inquiryForm.name}
Email: ${inquiryForm.email}
Phone: ${inquiryForm.phone}
Company: ${inquiryForm.company}
Message: ${inquiryForm.message}
Interested in: ${inquiryForm.tier} tier`;

    const whatsappUrl = `https://wa.me/27794484159?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
    setInquiryOpen(false);
  };

  const subscriptionTiers = [
    {
      id: 'beta',
      name: 'BETA',
      price: 'Free',
      duration: '30 days trial',
      popular: false,
      features: [
        'Animals Module (50 animals max)',
        'Basic Health Monitoring',
        'Feed Management (Basic)',
        'Excel-only Reports',
        'Community Support',
        'Mobile App Access'
      ],
      limitations: [
        'Limited to 50 animals',
        'Basic features only',
        'No advanced analytics',
        'No marketplace access'
      ],
      color: '#22C55E',
      action: () => navigate('/auth/beta')
    },
    {
      id: 'professional',
      name: 'Professional',
      price: 'R699',
      duration: '/month',
      popular: true,
      features: [
        'Everything in BETA',
        'Unlimited Animals',
        'Partnership & Resources Marketplace',
        'Auto-task System',
        'Service Provider Network',
        'Advanced Analytics',
        'In-app Communication',
        'Contract Generation',
        'Priority Support'
      ],
      limitations: [],
      color: '#3B82F6',
      action: () => navigate('/auth/professional')
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      price: 'Contact Us',
      duration: 'Custom Solutions',
      popular: false,
      features: [
        'Everything in Professional',
        'Custom Integrations',
        'Dedicated Account Manager',
        'On-site Training',
        'Custom Reporting',
        'API Access',
        'White-label Options',
        '24/7 Premium Support'
      ],
      limitations: [],
      color: '#A855F7',
      action: () => navigate('/auth/enterprise')
    }
  ];

  return (
    <div className="professional-landing">
      {/* Navigation Header */}
      <AppBar position="fixed" sx={{
        background: 'linear-gradient(135deg, rgba(34, 197, 94, 0.95) 0%, rgba(59, 130, 246, 0.95) 100%)',
        backdropFilter: 'blur(10px)'
      }}>
        <Toolbar>
          <Box sx={{ flexGrow: 1, display: 'flex', alignItems: 'center' }}>
            <AgriIntelBrand variant="compact" size="medium" color="white" />
          </Box>
          <Tabs
            value={currentTab}
            onChange={handleTabChange}
            sx={{
              '& .MuiTab-root': { color: 'white', fontWeight: 600 },
              '& .Mui-selected': { color: '#FEF3C7 !important' }
            }}
          >
            <Tab label="Home" />
            <Tab label="About Us" />
            <Tab label="Our Services" />
            <Tab label="Contact" />
          </Tabs>
          <Box sx={{ ml: 2 }}>
            <Button
              variant="contained"
              onClick={() => navigate('/login')}
              sx={{
                background: 'linear-gradient(45deg, #F59E0B, #EF4444)',
                fontWeight: 600
              }}
            >
              Login
            </Button>
          </Box>
        </Toolbar>
      </AppBar>

      <main>
        {/* Tab Content */}
        <TabPanel value={currentTab} index={0}>
          {/* Hero Section */}
          <section className="hero-section">
            <div className="hero-content">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <AgriIntelBrand
                  variant="full"
                  size="extra-large"
                  color="white"
                  orientation="vertical"
                  animated={true}
                />
              </motion.div>
              <motion.p
                className="hero-subtitle"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                Harness the power of AI to drive productivity, profitability, and sustainability in South African farming.
              </motion.p>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <Button
                  variant="contained"
                  size="large"
                  onClick={() => navigate('/register')}
                  sx={{
                    background: 'linear-gradient(45deg, #22C55E, #3B82F6)',
                    fontSize: '1.2rem',
                    px: 4,
                    py: 1.5,
                    borderRadius: '50px',
                    fontWeight: 600,
                    boxShadow: '0 4px 15px rgba(34, 197, 94, 0.4)',
                    '&:hover': {
                      transform: 'translateY(-3px)',
                      boxShadow: '0 6px 20px rgba(34, 197, 94, 0.6)'
                    }
                  }}
                >
                  Start Your Free Trial
                </Button>
              </motion.div>
            </div>
          </section>

          {/* Pricing Section */}
          <Container maxWidth="lg" sx={{ py: 8 }}>
            <Typography variant="h2" align="center" sx={{
              mb: 2,
              fontWeight: 700,
              background: 'linear-gradient(135deg, #22C55E, #3B82F6, #A855F7)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}>
              Choose Your Plan
            </Typography>
            <Typography variant="h6" align="center" color="text.secondary" sx={{ mb: 6, maxWidth: 600, mx: 'auto' }}>
              Start for free and scale as you grow. All plans come with our commitment to your success in South African farming.
            </Typography>

            <Grid container spacing={4} justifyContent="center">
              {subscriptionTiers.map((tier, index) => (
                <Grid item xs={12} md={4} key={tier.id}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.2 }}
                    whileHover={{ y: -10 }}
                  >
                    <Card
                      sx={{
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        position: 'relative',
                        border: tier.popular ? `2px solid ${tier.color}` : '1px solid',
                        borderColor: tier.popular ? tier.color : 'divider',
                        transform: tier.popular ? 'scale(1.05)' : 'scale(1)',
                        boxShadow: tier.popular ? `0 10px 30px ${tier.color}40` : 3,
                        '&:hover': {
                          boxShadow: `0 15px 40px ${tier.color}60`
                        }
                      }}
                    >
                      {tier.popular && (
                        <Chip
                          label="Most Popular"
                          color="primary"
                          size="small"
                          sx={{
                            position: 'absolute',
                            top: -10,
                            left: '50%',
                            transform: 'translateX(-50%)',
                            zIndex: 1,
                            background: `linear-gradient(45deg, ${tier.color}, #F59E0B)`
                          }}
                        />
                      )}

                      <CardContent sx={{ flexGrow: 1, textAlign: 'center', p: 3 }}>
                        <Typography variant="h4" sx={{
                          fontWeight: 600,
                          color: tier.color,
                          mb: 1
                        }}>
                          {tier.name}
                        </Typography>

                        <Typography variant="h3" sx={{
                          fontWeight: 700,
                          color: 'text.primary',
                          mb: 1
                        }}>
                          {tier.price}
                          {tier.duration && (
                            <Typography component="span" variant="body1" color="text.secondary">
                              {tier.duration}
                            </Typography>
                          )}
                        </Typography>

                        <Box sx={{ textAlign: 'left', mt: 3 }}>
                          {tier.features.map((feature, idx) => (
                            <Box key={idx} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                              <CheckCircle sx={{ color: tier.color, mr: 1, fontSize: 20 }} />
                              <Typography variant="body2">{feature}</Typography>
                            </Box>
                          ))}
                        </Box>

                        {tier.limitations.length > 0 && (
                          <Typography variant="caption" color="text.secondary" sx={{
                            mt: 2,
                            display: 'block',
                            fontStyle: 'italic'
                          }}>
                            {tier.limitations.join(', ')}
                          </Typography>
                        )}
                      </CardContent>

                      <CardActions sx={{ p: 3, pt: 0 }}>
                        <Button
                          fullWidth
                          variant={tier.popular ? "contained" : "outlined"}
                          size="large"
                          onClick={tier.action}
                          sx={{
                            background: tier.popular ? `linear-gradient(45deg, ${tier.color}, #F59E0B)` : 'transparent',
                            borderColor: tier.color,
                            color: tier.popular ? 'white' : tier.color,
                            fontWeight: 600,
                            py: 1.5,
                            '&:hover': {
                              background: tier.popular ? `linear-gradient(45deg, ${tier.color}dd, #F59E0Bdd)` : `${tier.color}10`
                            }
                          }}
                        >
                          {tier.id === 'enterprise' ? 'Contact Sales' : 'Get Started'}
                        </Button>
                      </CardActions>
                    </Card>
                  </motion.div>
                </Grid>
              ))}
            </Grid>
          </Container>
        </TabPanel>

        {/* About Us Tab */}
        <TabPanel value={currentTab} index={1}>
          <Container maxWidth="lg" sx={{ py: 8 }}>
            <Typography variant="h2" align="center" sx={{ mb: 6, fontWeight: 700 }}>
              About AgriIntel
            </Typography>

            <Grid container spacing={6} alignItems="center">
              <Grid item xs={12} md={6}>
                <Typography variant="h4" sx={{ mb: 3, fontWeight: 600 }}>
                  Revolutionizing South African Agriculture
                </Typography>
                <Typography variant="body1" sx={{ mb: 3, lineHeight: 1.8 }}>
                  AgriIntel is a comprehensive livestock management platform designed specifically for South African farmers.
                  We combine cutting-edge technology with deep understanding of local farming challenges to deliver
                  intelligent solutions that drive productivity, profitability, and sustainability.
                </Typography>
                <Typography variant="body1" sx={{ mb: 3, lineHeight: 1.8 }}>
                  Our platform supports all 11 official South African languages and integrates with local service providers
                  to create a complete ecosystem for modern farming operations.
                </Typography>

                <Grid container spacing={3} sx={{ mt: 4 }}>
                  <Grid item xs={6}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Agriculture sx={{ fontSize: 48, color: '#22C55E', mb: 1 }} />
                      <Typography variant="h6" fontWeight={600}>Smart Farming</Typography>
                      <Typography variant="body2" color="text.secondary">
                        AI-powered insights for better decisions
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box sx={{ textAlign: 'center' }}>
                      <TrendingUp sx={{ fontSize: 48, color: '#3B82F6', mb: 1 }} />
                      <Typography variant="h6" fontWeight={600}>Growth Focused</Typography>
                      <Typography variant="body2" color="text.secondary">
                        Scale your operations efficiently
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </Grid>

              <Grid item xs={12} md={6}>
                <Paper
                  elevation={8}
                  sx={{
                    p: 4,
                    background: 'linear-gradient(135deg, #22C55E10, #3B82F610)',
                    borderRadius: 3
                  }}
                >
                  <Typography variant="h5" sx={{ mb: 3, fontWeight: 600 }}>
                    Why Choose AgriIntel?
                  </Typography>
                  <Box sx={{ mb: 2 }}>
                    <CheckCircle sx={{ color: '#22C55E', mr: 1, verticalAlign: 'middle' }} />
                    <Typography component="span">Built for South African farmers</Typography>
                  </Box>
                  <Box sx={{ mb: 2 }}>
                    <CheckCircle sx={{ color: '#22C55E', mr: 1, verticalAlign: 'middle' }} />
                    <Typography component="span">Multi-language support (11 SA languages)</Typography>
                  </Box>
                  <Box sx={{ mb: 2 }}>
                    <CheckCircle sx={{ color: '#22C55E', mr: 1, verticalAlign: 'middle' }} />
                    <Typography component="span">Local service provider network</Typography>
                  </Box>
                  <Box sx={{ mb: 2 }}>
                    <CheckCircle sx={{ color: '#22C55E', mr: 1, verticalAlign: 'middle' }} />
                    <Typography component="span">Compliance with SA regulations</Typography>
                  </Box>
                  <Box sx={{ mb: 2 }}>
                    <CheckCircle sx={{ color: '#22C55E', mr: 1, verticalAlign: 'middle' }} />
                    <Typography component="span">24/7 support in your language</Typography>
                  </Box>
                </Paper>
              </Grid>
            </Grid>
          </Container>
        </TabPanel>

        {/* Our Services Tab */}
        <TabPanel value={currentTab} index={2}>
          <Container maxWidth="lg" sx={{ py: 8 }}>
            <Typography variant="h2" align="center" sx={{ mb: 6, fontWeight: 700 }}>
              Our Services
            </Typography>

            <Grid container spacing={4}>
              <Grid item xs={12} md={4}>
                <Card sx={{ height: '100%', textAlign: 'center', p: 3 }}>
                  <Agriculture sx={{ fontSize: 64, color: '#22C55E', mb: 2 }} />
                  <Typography variant="h5" sx={{ mb: 2, fontWeight: 600 }}>
                    Livestock Management
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    Comprehensive animal tracking, health monitoring, and breeding management
                    with AI-powered insights for optimal farm productivity.
                  </Typography>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card sx={{ height: '100%', textAlign: 'center', p: 3 }}>
                  <Analytics sx={{ fontSize: 64, color: '#3B82F6', mb: 2 }} />
                  <Typography variant="h5" sx={{ mb: 2, fontWeight: 600 }}>
                    Advanced Analytics
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    Data-driven insights, predictive analytics, and comprehensive reporting
                    to help you make informed decisions for your farming operations.
                  </Typography>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card sx={{ height: '100%', textAlign: 'center', p: 3 }}>
                  <Security sx={{ fontSize: 64, color: '#A855F7', mb: 2 }} />
                  <Typography variant="h5" sx={{ mb: 2, fontWeight: 600 }}>
                    Service Provider Network
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    Connect with veterinarians, suppliers, auctioneers, and security services
                    through our integrated marketplace platform.
                  </Typography>
                </Card>
              </Grid>
            </Grid>
          </Container>
        </TabPanel>

        {/* Contact Tab */}
        <TabPanel value={currentTab} index={3}>
          <Container maxWidth="lg" sx={{ py: 8 }}>
            <Typography variant="h2" align="center" sx={{ mb: 6, fontWeight: 700 }}>
              Contact Us
            </Typography>

            <Grid container spacing={6}>
              <Grid item xs={12} md={6}>
                <Typography variant="h4" sx={{ mb: 3, fontWeight: 600 }}>
                  Get in Touch
                </Typography>
                <Typography variant="body1" sx={{ mb: 4, lineHeight: 1.8 }}>
                  Ready to transform your farming operations? Contact us today to learn more about
                  AgriIntel and how we can help you achieve your agricultural goals.
                </Typography>

                <Box sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Phone sx={{ color: '#22C55E', mr: 2 }} />
                    <Typography variant="body1">
                      Primary: +27 79 448 4159
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Phone sx={{ color: '#22C55E', mr: 2 }} />
                    <Typography variant="body1">
                      Secondary: +27 82 990 8204
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Email sx={{ color: '#3B82F6', mr: 2 }} />
                    <Typography variant="body1">
                      <EMAIL>
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{ display: 'flex', gap: 2 }}>
                  <Button
                    variant="contained"
                    startIcon={<WhatsApp />}
                    onClick={() => window.open('https://wa.me/27794484159', '_blank')}
                    sx={{
                      background: '#25D366',
                      '&:hover': { background: '#20BA5A' }
                    }}
                  >
                    WhatsApp Chat
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<Phone />}
                    onClick={() => window.open('tel:+27794484159', '_self')}
                    sx={{ borderColor: '#22C55E', color: '#22C55E' }}
                  >
                    Call Now
                  </Button>
                </Box>
              </Grid>

              <Grid item xs={12} md={6}>
                <Paper elevation={3} sx={{ p: 4, borderRadius: 3 }}>
                  <Typography variant="h5" sx={{ mb: 3, fontWeight: 600 }}>
                    Quick Inquiry
                  </Typography>
                  <Button
                    fullWidth
                    variant="contained"
                    size="large"
                    onClick={() => setInquiryOpen(true)}
                    sx={{
                      background: 'linear-gradient(45deg, #22C55E, #3B82F6)',
                      py: 2,
                      fontSize: '1.1rem',
                      fontWeight: 600
                    }}
                  >
                    Send Enterprise Inquiry
                  </Button>
                </Paper>
              </Grid>
            </Grid>
          </Container>
        </TabPanel>

        {/* Mobile App Announcement */}
        <Box sx={{
          background: 'linear-gradient(135deg, #1F2937, #374151)',
          color: 'white',
          py: 4,
          textAlign: 'center'
        }}>
          <Container>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 3 }}>
              <Typography variant="h2">📱</Typography>
              <Typography variant="h5" fontWeight={500}>
                Mobile App Coming Soon! Manage your farm on the go.
              </Typography>
            </Box>
          </Container>
        </Box>
      </main>

      {/* WhatsApp Floating Button */}
      <Fab
        color="success"
        sx={{
          position: 'fixed',
          bottom: 20,
          right: 20,
          background: '#25D366',
          '&:hover': { background: '#20BA5A' }
        }}
        onClick={() => window.open('https://wa.me/27794484159', '_blank')}
      >
        <WhatsApp />
      </Fab>

      {/* Enterprise Inquiry Dialog */}
      <Dialog open={inquiryOpen} onClose={() => setInquiryOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          Enterprise Inquiry
          <IconButton onClick={() => setInquiryOpen(false)}>
            <Close />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Full Name"
                value={inquiryForm.name}
                onChange={(e) => setInquiryForm({...inquiryForm, name: e.target.value})}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={inquiryForm.email}
                onChange={(e) => setInquiryForm({...inquiryForm, email: e.target.value})}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone Number"
                value={inquiryForm.phone}
                onChange={(e) => setInquiryForm({...inquiryForm, phone: e.target.value})}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Company/Farm Name"
                value={inquiryForm.company}
                onChange={(e) => setInquiryForm({...inquiryForm, company: e.target.value})}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Message"
                multiline
                rows={4}
                value={inquiryForm.message}
                onChange={(e) => setInquiryForm({...inquiryForm, message: e.target.value})}
                placeholder="Tell us about your farming operation and requirements..."
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button onClick={() => setInquiryOpen(false)}>Cancel</Button>
          <Button
            variant="contained"
            onClick={handleInquirySubmit}
            startIcon={<WhatsApp />}
            sx={{
              background: '#25D366',
              '&:hover': { background: '#20BA5A' }
            }}
          >
            Send via WhatsApp
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default ProfessionalLanding;
