import React from 'react';
import { Box, Typography, Container } from '@mui/material';
import ServiceMarketplace from '../components/marketplace/ServiceMarketplace';
import { useThemeContext } from '../contexts/ThemeContext';

// Mock Data
const initialPartners = [
  { id: 1, name: 'Dr. <PERSON>', service: 'Veterinarian', availability: 'Mon-Fri, 9am-5pm', avatar: <FaUserMd /> },
  { id: 2, name: 'Agri-Auctions Inc.', service: 'Auctioneer', availability: 'By Appointment', avatar: <FaGavel /> },
  { id: 3, name: 'Farm-to-Gate Logistics', service: 'Transport', availability: '24/7', avatar: <FaTruck /> },
  { id: 4, name: 'CattleGuard Security', service: 'Security', availability: '24/7', avatar: <FaShieldAlt /> },
  { id: 5, name: 'CropCare Suppliers', service: 'Supplies', availability: 'Mon-Sat, 8am-6pm', avatar: <FaHandshake /> },
];

const initialActiveRequests = [
  { id: 1, partnerName: '<PERSON>. <PERSON>', service: 'Emergency Check-up', status: 'In Progress' },
  { id: 2, partnerName: 'Agri-Auctions Inc.', service: 'Cattle Valuation', status: 'Pending' },
  { id: 3, partnerName: 'Farm-to-Gate Logistics', service: 'Transport to Market', status: 'Completed' },
];

// Helper to get user's subscription tier
const getUserTier = () => localStorage.getItem('userTier') || 'basic';

const PartnershipAndResources: React.FC = () => {
  const [partners] = useState(initialPartners);
  const [activeRequests, setActiveRequests] = useState(initialActiveRequests);
  const [partnerNotifications, setPartnerNotifications] = useState<string[]>([]);
  const [userTier, setUserTier] = useState('basic');

  useEffect(() => {
    setUserTier(getUserTier());
  }, []);

  const handleRequestService = (partner: typeof initialPartners[0]) => {
    const newRequest = {
      id: activeRequests.length + 1,
      partnerName: partner.name,
      service: `Service from ${partner.name}`,
      status: 'Pending',
    };
    setActiveRequests(prev => [...prev, newRequest]);
    setPartnerNotifications(prev => [...prev, `Notification: New service request for ${partner.name}.`]);
  };

  const handleRequestStatusChange = (id: number, status: 'In Progress' | 'Declined' | 'Completed') => {
    if (status === 'Declined') {
      setActiveRequests(prev => prev.filter(req => req.id !== id));
    } else {
      setActiveRequests(prev => prev.map(req => req.id === id ? { ...req, status } : req));
    }
  };

  if (userTier !== 'professional') {
    return (
      <Box sx={{ p: 4, textAlign: 'center' }}>
        <Typography variant="h5">This feature is available on the Professional Plan.</Typography>
        <Typography>Upgrade to manage partnerships and service requests.</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 4, backgroundColor: '#f7f9fc', minHeight: '100vh' }}>
      <Typography variant="h4" component="h1" sx={{ color: '#0D1B2A', borderBottom: '3px solid #3DDC97', paddingBottom: '10px', marginBottom: '2rem', display: 'flex', alignItems: 'center' }}>
        <FaHandshake style={{ marginRight: '15px', color: '#3DDC97' }} />
        Partners & Resources
      </Typography>

      {/* Partner Directory */}
      <Paper sx={{ p: 3, mb: 4, borderRadius: '12px', boxShadow: '0 8px 24px rgba(0,0,0,0.05)' }}>
        <Typography variant="h5" component="h2" sx={{ color: '#415A77', mb: 3 }}>
          Partner Directory
        </Typography>
        <Grid container spacing={3}>
          {partners.map((partner) => (
            <Grid item xs={12} sm={6} md={4} key={partner.id}>
              <Paper elevation={2} sx={{ p: 2, borderRadius: '8px', display: 'flex', flexDirection: 'column', height: '100%', transition: 'transform 0.2s', '&:hover': { transform: 'scale(1.03)' } }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar sx={{ bgcolor: '#E0E1DD', color: '#415A77', width: 50, height: 50, mr: 2 }}>
                    {partner.avatar}
                  </Avatar>
                  <Box>
                    <Typography variant="h6" sx={{ color: '#0D1B2A' }}>{partner.name}</Typography>
                    <Typography variant="body2" color="textSecondary">{partner.service}</Typography>
                  </Box>
                </Box>
                <Typography variant="body2" sx={{ flexGrow: 1, mb: 2 }}>
                  Availability: {partner.availability}
                </Typography>
                <Button variant="contained" onClick={() => handleRequestService(partner)} sx={{ mt: 'auto', backgroundColor: '#3DDC97', '&:hover': { backgroundColor: '#35c285' } }}>
                  Request Service
                </Button>
              </Paper>
            </Grid>
          ))}
        </Grid>
      </Paper>

      {/* Active Service Requests */}
      <Paper sx={{ p: 3, mb: 4, borderRadius: '12px', boxShadow: '0 8px 24px rgba(0,0,0,0.05)' }}>
        <Typography variant="h5" component="h2" sx={{ color: '#415A77', mb: 3 }}>
          Active Service Requests
        </Typography>
        <Box>
          {activeRequests.map((request) => (
            <Paper key={request.id} sx={{ p: 2, mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center', borderRadius: '8px', borderLeft: `5px solid ${request.status === 'Completed' ? '#2ecc71' : request.status === 'In Progress' ? '#3498db' : '#f1c40f'}` }}>
              <Box>
                <Typography variant="subtitle1">{request.service}</Typography>
                <Typography variant="body2" color="textSecondary">{request.partnerName}</Typography>
              </Box>
              <Box>
                {request.status === 'Pending' && (
                  <>
                    <Button size="small" onClick={() => handleRequestStatusChange(request.id, 'In Progress')} sx={{ mr: 1 }}>Accept</Button>
                    <Button size="small" color="error" onClick={() => handleRequestStatusChange(request.id, 'Declined')}>Decline</Button>
                  </>
                )}
                {request.status === 'In Progress' && (
                  <Button size="small" onClick={() => handleRequestStatusChange(request.id, 'Completed')}>Mark as Complete</Button>
                )}
                <Chip
                  label={request.status}
                  color={request.status === 'Completed' ? 'success' : request.status === 'In Progress' ? 'info' : 'warning'}
                />
              </Box>
            </Paper>
          ))}
        </Box>
      </Paper>

      {/* Partner Notifications Log */}
      <Paper sx={{ p: 3, borderRadius: '12px', boxShadow: '0 8px 24px rgba(0,0,0,0.05)' }}>
        <Typography variant="h5" component="h2" sx={{ color: '#415A77', mb: 3 }}>
          Partner Notifications
        </Typography>
        <Box>
          {partnerNotifications.map((notification, index) => (
            <Alert key={index} severity="info" sx={{ mb: 1 }}>{notification}</Alert>
          ))}
        </Box>
      </Paper>
    </Box>
  );
};

export default PartnershipAndResources;