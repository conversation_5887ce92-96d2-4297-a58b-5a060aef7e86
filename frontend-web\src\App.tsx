import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';

// Import accessibility fixes
import './styles/accessibility-fixes.css';

// Core providers and context
import { ThemeProvider } from './contexts/ThemeContext';
import { AuthProvider } from './contexts/AuthContext';
import { SubscriptionProvider } from './contexts/SubscriptionContext';
import { SnackbarProvider } from './contexts/SnackbarContext';
import { MongoDbProvider } from './contexts/SimpleMongoDbContext';
import { DataProvider } from './contexts/DataContext';
import { LanguageProvider } from './contexts/LanguageContext';

// Core components
import { LazyLoadFallback } from './components/common';
import ErrorBoundary from './components/error/ComprehensiveErrorBoundary';

// Utilities and hooks
import { lazyWithRetry } from './utils/lazyLoadUtils';

// Lazy load components with enhanced error handling and retry logic
// Main entry point components
const LandingPage = lazyWithRetry(() => import('./pages/SimpleLanding'));
const EnhancedBeta = lazyWithRetry(() => import('./pages/EnhancedBeta'));
const BetaOnlyDashboard = lazyWithRetry(() => import('./pages/BetaOnlyDashboard'));
const LiveOnlyDashboard = lazyWithRetry(() => import('./pages/LiveOnlyDashboard'));
const EnhancedRegister = lazyWithRetry(() => import('./pages/EnhancedRegister'));

// Authentication components
const Login = lazyWithRetry(() => import('./pages/Login'));
const BetaLogin = lazyWithRetry(() => import('./pages/BetaLogin'));
const PremiumLogin = lazyWithRetry(() => import('./pages/PremiumLogin'));
const ProLogin = lazyWithRetry(() => import('./pages/ProLogin'));
const Register = lazyWithRetry(() => import('./pages/Register'));

// Professional Landing Page
const ProfessionalLanding = lazyWithRetry(() => import('./pages/ProfessionalLanding'));

// Dashboard components
const Dashboard = lazyWithRetry(() => import('./pages/dashboard/Dashboard'));
const UnifiedDashboardLayout = lazyWithRetry(() => import('./layouts/UnifiedDashboardLayout'));

// Module components - All 12 main modules
const Animals = lazyWithRetry(() => import('./pages/animals/Animals'));
const Health = lazyWithRetry(() => import('./pages/Health'));
const Breeding = lazyWithRetry(() => import('./pages/breeding/Breeding'));
const Feeding = lazyWithRetry(() => import('./pages/feeding/Feeding'));
const Financial = lazyWithRetry(() => import('./pages/Financial'));
const Inventory = lazyWithRetry(() => import('./pages/inventory/InventoryDashboard'));
const Commercial = lazyWithRetry(() => import('./pages/commercial/Commercial'));
const Reports = lazyWithRetry(() => import('./pages/Reports'));
const Resources = lazyWithRetry(() => import('./pages/Resources'));
const Settings = lazyWithRetry(() => import('./pages/Settings'));
const Compliance = lazyWithRetry(() => import('./pages/Compliance'));
const Analytics = lazyWithRetry(() => import('./pages/Analytics'));

// Business Analysis
const BusinessAnalysis = lazyWithRetry(() => import('./pages/BusinessAnalysis'));

function App() {
  return (
    <ErrorBoundary>
      <LanguageProvider>
        <ThemeProvider>
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <SnackbarProvider>
              <MongoDbProvider>
                <DataProvider>
                  <AuthProvider>
                    <SubscriptionProvider>
                      <Router>
                        <Suspense fallback={<LazyLoadFallback />}>
                          <Routes>
                            {/* Landing and Authentication Routes */}
                            <Route path="/" element={<ProfessionalLanding />} />
                            <Route path="/simple" element={<LandingPage />} />
                            <Route path="/login" element={<Login />} />
                            <Route path="/premium-login" element={<PremiumLogin />} />
                            <Route path="/pro-login" element={<ProLogin />} />
                            <Route path="/beta-login" element={<BetaLogin />} />
                            <Route path="/register" element={<Register />} />
                            <Route path="/enhanced-beta" element={<EnhancedBeta />} />
                            <Route path="/enhanced-register" element={<EnhancedRegister />} />

                            {/* Dashboard Routes - Both Beta and Live users get proper dashboard with sidebar */}
                            <Route path="/dashboard/*" element={<UnifiedDashboardLayout />}>
                              <Route index element={<Dashboard />} />
                              <Route path="animals/*" element={<Animals />} />
                              <Route path="health/*" element={<Health />} />
                              <Route path="breeding/*" element={<Breeding />} />
                              <Route path="feeding/*" element={<Feeding />} />
                              <Route path="financial/*" element={<Financial />} />
                              <Route path="inventory/*" element={<Inventory />} />
                              <Route path="commercial/*" element={<Commercial />} />
                              <Route path="reports/*" element={<Reports />} />
                              <Route path="resources/*" element={<Resources />} />
                              <Route path="settings/*" element={<Settings />} />
                              <Route path="compliance/*" element={<Compliance />} />
                            </Route>

                            <Route path="/beta-dashboard/*" element={<UnifiedDashboardLayout />}>
                              <Route index element={<Dashboard />} />
                              <Route path="animals/*" element={<Animals />} />
                              <Route path="health/*" element={<Health />} />
                              <Route path="financial/*" element={<Financial />} />
                              <Route path="feeding/*" element={<Feeding />} />
                              <Route path="resources/*" element={<Resources />} />
                              <Route path="settings/*" element={<Settings />} />
                            </Route>

                            {/* Live Dashboard Routes - Full access for live users */}
                            <Route path="/live-dashboard/*" element={<UnifiedDashboardLayout />}>
                              <Route index element={<Dashboard />} />
                              <Route path="animals/*" element={<Animals />} />
                              <Route path="health/*" element={<Health />} />
                              <Route path="breeding/*" element={<Breeding />} />
                              <Route path="feeding/*" element={<Feeding />} />
                              <Route path="financial/*" element={<Financial />} />
                              <Route path="inventory/*" element={<Inventory />} />
                              <Route path="commercial/*" element={<Commercial />} />
                              <Route path="reports/*" element={<Reports />} />
                              <Route path="resources/*" element={<Resources />} />
                              <Route path="settings/*" element={<Settings />} />
                              <Route path="compliance/*" element={<Compliance />} />
                            </Route>
                          </Routes>
                        </Suspense>
                      </Router>
                    </SubscriptionProvider>
                  </AuthProvider>
                </DataProvider>
              </MongoDbProvider>
            </SnackbarProvider>
          </LocalizationProvider>
        </ThemeProvider>
      </LanguageProvider>
    </ErrorBoundary>
  );
}

export default App;