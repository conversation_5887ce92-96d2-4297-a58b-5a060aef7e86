import React from 'react';
import { Routes, Route } from 'react-router-dom';
import AnimalManagement from '../pages/AnimalManagement';
import FeedAndFinancials from '../pages/FeedAndFinancials';
import PartnershipAndResources from '../pages/PartnershipAndResources';
import AgriIntelLanding from '../pages/AgriIntelLanding';
import BetaAnimalsModule from '../components/beta/BetaAnimalsModule';
import BetaFeedModule from '../components/beta/BetaFeedModule';
import BetaReportsModule from '../components/beta/BetaReportsModule';
import SubscriptionPlans from '../components/subscription/SubscriptionPlans';
import ServiceMarketplace from '../components/marketplace/ServiceMarketplace';
import AutoTaskSystem from '../components/marketplace/AutoTaskSystem';
import ContractPaymentSystem from '../components/marketplace/ContractPaymentSystem';
import BetaLogin from '../pages/auth/BetaLogin';
import ProfessionalLogin from '../pages/auth/ProfessionalLogin';
import EnterpriseLogin from '../pages/auth/EnterpriseLogin';
import Register from '../pages/auth/Register';
import ProtectedRoute from '../components/auth/ProtectedRoute';

const BetaRoutes: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={<AgriIntelLanding />} />

      {/* Auth Routes */}
      <Route path="/login/beta" element={<BetaLogin />} />
      <Route path="/login/professional" element={<ProfessionalLogin />} />
      <Route path="/register" element={<Register />} />

      {/* BETA Module Routes */}
      <Route path="/beta/animals" element={<ProtectedRoute element={<BetaAnimalsModule userTier="beta" />} allowedTiers={['beta', 'professional']} />} />
      <Route path="/beta/feed" element={<ProtectedRoute element={<BetaFeedModule userTier="beta" />} allowedTiers={['beta', 'professional']} />} />
      <Route path="/beta/reports" element={<ProtectedRoute element={<BetaReportsModule userTier="beta" />} allowedTiers={['beta', 'professional']} />} />

      <Route path="/animals" element={<ProtectedRoute element={<AnimalManagement />} allowedTiers={['professional']} />} />
      <Route path="/feed-and-financials" element={<ProtectedRoute element={<FeedAndFinancials />} allowedTiers={['professional']} />} />
      <Route path="/partners-and-resources" element={<ProtectedRoute element={<PartnershipAndResources />} allowedTiers={['professional']} />} />

      {/* Subscription Routes */}
      <Route path="/subscription" element={<SubscriptionPlans />} />
      <Route path="/subscription/plans" element={<SubscriptionPlans />} />

      {/* Professional Routes */}
      <Route path="/marketplace" element={<ProtectedRoute element={<ServiceMarketplace />} allowedTiers={['professional']} />} />
      <Route path="/marketplace/services" element={<ProtectedRoute element={<ServiceMarketplace />} allowedTiers={['professional']} />} />
      <Route path="/marketplace/auto-tasks" element={<ProtectedRoute element={<AutoTaskSystem />} allowedTiers={['professional']} />} />
      <Route path="/marketplace/contracts" element={<ProtectedRoute element={<ContractPaymentSystem />} allowedTiers={['professional']} />} />
      
      {/* Default redirect to subscription for upgrade prompts */}
      <Route path="/upgrade" element={<SubscriptionPlans />} />
    </Routes>
  );
};

export default BetaRoutes;
