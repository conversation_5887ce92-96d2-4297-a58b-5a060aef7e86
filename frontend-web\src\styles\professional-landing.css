/*
 * AMPD-LIVESTOCK - CONVERSION-OPTIMIZED LANDING PAGE
 *
 * Description:
 * This stylesheet implements a visually stunning and conversion-optimized landing page.
 * The design is inspired by premium WordPress templates, focusing on a clean, modern aesthetic
 * that drives user sign-ups for a multi-tiered SaaS product.
 *
 * Key Sections:
 * 1.  Global Styles & Variables: Root variables for a refined color palette and typography.
 * 2.  Hero Section: Full-bleed, atmospheric hero with blended background image.
 * 3.  Pricing Section: Three-tiered, interactive pricing cards.
 * 4.  Mobile App Announcement: A non-intrusive "coming soon" banner.
 * 5.  Responsive Design: Fluid adjustments for all screen sizes.
 */

:root {
    --primary-font: 'Poppins', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --secondary-font: 'Roboto', sans-serif;
    --primary-color: #0D1B2A; /* Dark Blue */
    --secondary-color: #415A77; /* Shadow Blue */
    --accent-color: #E0E1DD; /* Platinum */
    --text-light: #F0F3F5;
    --text-dark: #1B263B;
    --cta-color: #3DDC97; /* Bright Green */
    --white: #ffffff;
    --shadow: rgba(0, 0, 0, 0.15);
}

body {
    font-family: var(--primary-font);
    background-color: #f7f9fc;
    color: var(--text-dark);
    margin: 0;
    line-height: 1.7;
}

.professional-landing {
    overflow-x: hidden;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Main Content Spacing */
.professional-landing main {
    margin-top: 64px;
}

/* Hero Section */
.hero-section {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: var(--white);
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%),
                url('/images/modules/animals/cattle-1.jpeg') no-repeat center center/cover;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(13, 27, 42, 0.8), rgba(65, 90, 119, 0.6));
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
}

.hero-title {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.5);
}

.hero-subtitle {
    font-size: 1.4rem;
    font-family: var(--secondary-font);
    margin-bottom: 2.5rem;
    opacity: 0.9;
}

.btn {
    padding: 1rem 2.5rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background-color: var(--cta-color);
    color: var(--text-dark);
    box-shadow: 0 4px 15px rgba(61, 220, 151, 0.4);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(61, 220, 151, 0.6);
}

/* Pricing Section */
.pricing-section {
    padding: 6rem 0;
    background-color: #f7f9fc;
}

.section-title {
    text-align: center;
    font-size: 2.8rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.section-subtitle {
    text-align: center;
    font-size: 1.2rem;
    color: var(--secondary-color);
    margin-bottom: 4rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    align-items: center;
}

.pricing-card {
    background: var(--white);
    border-radius: 15px;
    padding: 2.5rem;
    box-shadow: 0 10px 30px var(--shadow);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 2px solid transparent;
}

.pricing-card.professional {
    transform: scale(1.05);
    border-color: var(--cta-color);
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.tier-name {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.tier-price {
    font-size: 3rem;
    font-weight: 700;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.tier-price span {
    font-size: 1rem;
    font-weight: 400;
}

.tier-features {
    list-style: none;
    padding: 0;
    margin: 2rem 0;
    text-align: left;
}

.tier-features li {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.tier-features li::before {
    content: '✓';
    color: var(--cta-color);
    font-weight: bold;
    margin-right: 10px;
}

.tier-description {
    font-size: 0.9rem;
    color: var(--secondary-color);
    margin-top: 1.5rem;
    min-height: 50px;
}

.btn-secondary {
    background-color: transparent;
    color: var(--cta-color);
    border: 2px solid var(--cta-color);
}

.btn-secondary:hover {
    background-color: var(--cta-color);
    color: var(--text-dark);
}

/* Mobile App Announcement */
.app-announcement {
    background-color: var(--primary-color);
    color: var(--text-light);
    text-align: center;
    padding: 2rem 0;
}

.app-announcement-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.5rem;
}

.app-announcement-icon {
    font-size: 2.5rem;
}

.app-announcement-text {
    font-size: 1.2rem;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 992px) {
    .hero-title {
        font-size: 3rem;
    }
    .pricing-card.professional {
        transform: scale(1);
    }
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    .hero-subtitle {
        font-size: 1.2rem;
    }
    .section-title {
        font-size: 2.2rem;
    }
}
