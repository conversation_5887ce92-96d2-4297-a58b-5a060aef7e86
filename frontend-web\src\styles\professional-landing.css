/*
 * AGRIINTEL - PRODUCTION-READY LANDING PAGE
 *
 * Professional livestock management platform for South African farmers
 * Implementing exact Canva design specifications with AgriIntel branding
 *
 * Typography Standards:
 * - Primary headings: 52px+ (font-weight: 800)
 * - Secondary headings: 24px+ (font-weight: 600)
 * - Body text: 20px+ (font-weight: 400-500)
 * - Line height: 1.6-1.8 for optimal readability
 * - WCAG AA contrast compliance (4.5:1 minimum)
 *
 * AgriIntel Brand Colors (from Canva design):
 * - Deep Blues: #1E40AF, #3B82F6
 * - Emerald Greens: #059669, #10B981, #22C55E
 * - Warm Golds: #F59E0B, #FBBF24
 * - Supporting: #A855F7 (Enterprise purple)
 */

:root {
    /* AgriIntel Brand Typography */
    --primary-font: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    --secondary-font: 'Roboto', 'Helvetica Neue', Arial, sans-serif;

    /* AgriIntel Brand Colors - Exact Canva Specifications */
    --agri-deep-blue-primary: #1E40AF;
    --agri-deep-blue-secondary: #3B82F6;
    --agri-emerald-primary: #059669;
    --agri-emerald-secondary: #10B981;
    --agri-emerald-accent: #22C55E;
    --agri-gold-primary: #F59E0B;
    --agri-gold-secondary: #FBBF24;
    --agri-enterprise-purple: #A855F7;

    /* Gradient Combinations - AgriIntel Brand */
    --agri-gradient-primary: linear-gradient(135deg, var(--agri-emerald-accent) 0%, var(--agri-deep-blue-secondary) 100%);
    --agri-gradient-secondary: linear-gradient(135deg, var(--agri-deep-blue-primary) 0%, var(--agri-emerald-primary) 100%);
    --agri-gradient-gold: linear-gradient(135deg, var(--agri-gold-primary) 0%, var(--agri-gold-secondary) 100%);
    --agri-gradient-enterprise: linear-gradient(135deg, var(--agri-enterprise-purple) 0%, var(--agri-deep-blue-primary) 100%);

    /* Typography Scale - Production Standards */
    --font-size-hero: clamp(3.5rem, 8vw, 6rem); /* 56px-96px responsive */
    --font-size-h1: clamp(2.5rem, 5vw, 3.25rem); /* 40px-52px */
    --font-size-h2: clamp(2rem, 4vw, 2.5rem); /* 32px-40px */
    --font-size-h3: clamp(1.5rem, 3vw, 2rem); /* 24px-32px */
    --font-size-h4: clamp(1.25rem, 2.5vw, 1.5rem); /* 20px-24px */
    --font-size-body: clamp(1.25rem, 2vw, 1.375rem); /* 20px-22px minimum */
    --font-size-caption: clamp(1.125rem, 1.5vw, 1.25rem); /* 18px-20px */

    /* Spacing & Layout */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-2xl: 4rem;
    --spacing-3xl: 6rem;

    /* Effects & Shadows */
    --glassmorphism-bg: rgba(255, 255, 255, 0.15);
    --glassmorphism-border: rgba(255, 255, 255, 0.2);
    --backdrop-blur: blur(20px);
    --shadow-card: 0 25px 50px rgba(0, 0, 0, 0.25);
    --shadow-hover: 0 35px 60px rgba(0, 0, 0, 0.35);
    --border-radius-sm: 8px;
    --border-radius-md: 16px;
    --border-radius-lg: 24px;
    --border-radius-xl: 32px;

    /* Transitions - Smooth & Professional */
    --transition-fast: all 0.2s cubic-bezier(0.25, 0.8, 0.25, 1);
    --transition-smooth: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    --transition-slow: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);

    /* Text Colors */
    --text-primary: #1F2937;
    --text-secondary: #6B7280;
    --text-light: #F9FAFB;
    --white: #FFFFFF;
}

/* Global Styles - Production Ready */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: var(--primary-font);
    font-size: var(--font-size-body);
    line-height: 1.7;
    color: var(--text-primary);
    background-color: var(--white);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography Hierarchy - WCAG AA Compliant */
h1, .hero-title {
    font-size: var(--font-size-hero);
    font-weight: 800;
    line-height: 1.1;
    letter-spacing: -0.02em;
    color: var(--text-light);
}

h2, .section-title {
    font-size: var(--font-size-h1);
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: -0.01em;
    color: var(--text-primary);
}

h3, .feature-title {
    font-size: var(--font-size-h2);
    font-weight: 600;
    line-height: 1.3;
    color: var(--text-primary);
}

h4, .hero-subtitle {
    font-size: var(--font-size-h3);
    font-weight: 500;
    line-height: 1.5;
    color: var(--text-light);
}

h5, .section-subtitle {
    font-size: var(--font-size-h4);
    font-weight: 500;
    line-height: 1.4;
    color: var(--text-secondary);
}

h6, .rating-text {
    font-size: var(--font-size-caption);
    font-weight: 600;
    line-height: 1.4;
    color: var(--text-light);
}

/* AgriIntel Landing Page Container */
.agriintel-landing {
    position: relative;
    min-height: 100vh;
    overflow: hidden;
}

/* Dynamic Background with Livestock Images */
.landing-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    transition: var(--transition-slow);
}

/* Background images - set dynamically via JavaScript */
.landing-background {
    /* Background images will be set via inline styles from React component */
}

/* Gradient overlay for better text readability */
.landing-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--agri-gradient-secondary);
    opacity: 0.75;
    z-index: 1;
}

/* Background blend mode for professional image-gradient blending */
.landing-background::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        rgba(30, 64, 175, 0.8) 0%,
        rgba(5, 150, 105, 0.6) 50%,
        rgba(245, 158, 11, 0.4) 100%);
    mix-blend-mode: overlay;
    z-index: 2;
}

/* Navigation Header */
.landing-navigation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    background: var(--glassmorphism-bg);
    -webkit-backdrop-filter: var(--backdrop-blur);
    backdrop-filter: var(--backdrop-blur);
    border-bottom: 1px solid var(--glassmorphism-border);
    padding: var(--spacing-sm) 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 var(--spacing-lg);
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.agriintel-logo {
    font-size: clamp(1.5rem, 3vw, 2rem);
    font-weight: 800;
    color: var(--text-light);
    text-decoration: none;
    letter-spacing: -0.01em;
}

.nav-badge {
    background: var(--agri-gradient-gold) !important;
    color: var(--text-primary) !important;
    font-weight: 600 !important;
    font-size: 0.75rem !important;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.nav-button {
    font-weight: 600 !important;
    font-size: var(--font-size-caption) !important;
    padding: var(--spacing-sm) var(--spacing-lg) !important;
    border-radius: var(--border-radius-lg) !important;
    transition: var(--transition-smooth) !important;
    text-transform: none !important;
}

.nav-button-outline {
    border: 2px solid var(--text-light) !important;
    color: var(--text-light) !important;
    background: transparent !important;
}

.nav-button-outline:hover {
    background: var(--text-light) !important;
    color: var(--text-primary) !important;
    transform: translateY(-2px);
}

.nav-button-primary {
    background: var(--agri-gradient-primary) !important;
    color: var(--text-light) !important;
    border: none !important;
    box-shadow: var(--shadow-card);
}

.nav-button-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

/* Hero Section */
.hero-section {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: var(--spacing-3xl) 0;
    z-index: 10;
}

.hero-grid {
    align-items: center;
    min-height: 80vh;
}

.hero-content {
    padding: var(--spacing-xl) 0;
}

.hero-title {
    margin-bottom: var(--spacing-lg);
    text-shadow: 2px 4px 8px rgba(0, 0, 0, 0.3);
}

.hero-title-highlight {
    display: block;
    background: var(--agri-gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-top: var(--spacing-sm);
}

.hero-subtitle {
    margin-bottom: var(--spacing-xl);
    max-width: 600px;
    text-shadow: 1px 2px 4px rgba(0, 0, 0, 0.2);
}

.hero-stats {
    display: flex;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
    min-width: 120px;
}

.stat-number {
    font-size: var(--font-size-h1);
    font-weight: 800;
    color: var(--agri-gold-primary);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: var(--font-size-caption);
    color: var(--text-light);
    font-weight: 500;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.hero-actions {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
    flex-wrap: wrap;
}

.cta-primary {
    background: var(--agri-gradient-primary) !important;
    color: var(--text-light) !important;
    font-size: var(--font-size-h4) !important;
    font-weight: 700 !important;
    padding: var(--spacing-md) var(--spacing-2xl) !important;
    border-radius: var(--border-radius-xl) !important;
    border: none !important;
    box-shadow: var(--shadow-card);
    transition: var(--transition-smooth) !important;
    text-transform: none !important;
    min-width: 200px;
}

.cta-primary:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-hover);
}

.cta-secondary {
    background: transparent !important;
    color: var(--text-light) !important;
    font-size: var(--font-size-h4) !important;
    font-weight: 600 !important;
    padding: var(--spacing-md) var(--spacing-2xl) !important;
    border: 2px solid var(--text-light) !important;
    border-radius: var(--border-radius-xl) !important;
    transition: var(--transition-smooth) !important;
    text-transform: none !important;
    min-width: 200px;
}

.cta-secondary:hover {
    background: var(--text-light) !important;
    color: var(--text-primary) !important;
    transform: translateY(-4px);
}

.hero-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.rating-stars .MuiRating-icon {
    color: var(--agri-gold-primary) !important;
    font-size: 1.5rem !important;
}

.hero-image-container {
    position: relative;
    padding: var(--spacing-lg);
}

.hero-dashboard-preview {
    position: relative;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-card);
    background: var(--glassmorphism-bg);
    -webkit-backdrop-filter: var(--backdrop-blur);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--glassmorphism-border);
}

.hero-dashboard-image {
    width: 100%;
    height: auto;
    display: block;
    border-radius: var(--border-radius-lg);
}

.dashboard-overlay {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
}

.overlay-badge {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: var(--agri-gradient-primary);
    color: var(--text-light);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-caption);
    font-weight: 600;
    box-shadow: var(--shadow-card);
}

.badge-icon {
    font-size: 1rem !important;
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.section-title {
    margin-bottom: var(--spacing-md);
    background: var(--agri-gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    max-width: 600px;
    margin: 0 auto;
}

/* Features Section */
.features-section {
    position: relative;
    padding: var(--spacing-3xl) 0;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(249, 250, 251, 0.9) 100%);
    z-index: 10;
}

.features-grid {
    margin-top: var(--spacing-2xl);
}

.feature-card {
    position: relative;
    height: 400px;
    border-radius: var(--border-radius-lg) !important;
    overflow: hidden;
    background: var(--glassmorphism-bg) !important;
    -webkit-backdrop-filter: var(--backdrop-blur);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--glassmorphism-border) !important;
    box-shadow: var(--shadow-card) !important;
    transition: var(--transition-smooth) !important;
    cursor: pointer;
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-hover) !important;
}

.feature-card-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    opacity: 0.3;
    transition: var(--transition-smooth);
}

/* Feature card backgrounds - set dynamically if needed */
.feature-card-background {
    /* Background images will be set via inline styles if needed */
}

.feature-card:hover .feature-card-background {
    opacity: 0.5;
    transform: scale(1.05);
}

.feature-card-content {
    position: relative;
    z-index: 2;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: var(--spacing-xl) !important;
}

.feature-icon-container {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--agri-gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-card);
}

.feature-icon {
    font-size: 2.5rem !important;
    color: var(--text-light) !important;
}

.feature-title {
    margin-bottom: var(--spacing-md) !important;
    color: var(--text-primary) !important;
}

.feature-description {
    color: var(--text-secondary) !important;
    line-height: 1.6;
}

/* Pricing Section */
.pricing-section {
    position: relative;
    padding: var(--spacing-3xl) 0;
    background: linear-gradient(135deg,
        rgba(30, 64, 175, 0.05) 0%,
        rgba(5, 150, 105, 0.05) 50%,
        rgba(168, 85, 247, 0.05) 100%);
    z-index: 10;
}

.pricing-grid {
    margin-top: var(--spacing-2xl);
}

.pricing-card {
    position: relative;
    height: auto;
    min-height: 600px;
    border-radius: var(--border-radius-lg) !important;
    background: var(--glassmorphism-bg) !important;
    -webkit-backdrop-filter: var(--backdrop-blur);
    backdrop-filter: var(--backdrop-blur);
    border: 2px solid transparent !important;
    box-shadow: var(--shadow-card) !important;
    transition: var(--transition-smooth) !important;
    cursor: pointer;
    overflow: hidden;
}

.pricing-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-hover) !important;
}

.pricing-card-beta {
    border-color: var(--agri-emerald-accent) !important;
}

.pricing-card-professional {
    border-color: var(--agri-deep-blue-secondary) !important;
    transform: scale(1.05);
}

.pricing-card-enterprise {
    border-color: var(--agri-enterprise-purple) !important;
}

.pricing-card.popular {
    border-color: var(--agri-deep-blue-secondary) !important;
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.1) 0%,
        rgba(255, 255, 255, 0.15) 100%) !important;
}

.popular-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    background: var(--agri-gradient-primary) !important;
    color: var(--text-light) !important;
    font-weight: 700 !important;
    font-size: 0.875rem !important;
}

.pricing-card-content {
    padding: var(--spacing-2xl) !important;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.plan-name {
    text-align: center;
    margin-bottom: var(--spacing-sm) !important;
    font-weight: 700 !important;
}

.plan-description {
    text-align: center;
    color: var(--text-secondary) !important;
    margin-bottom: var(--spacing-lg) !important;
}

.plan-price {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.price-amount {
    font-weight: 800 !important;
    color: var(--text-primary) !important;
    margin-bottom: var(--spacing-xs) !important;
}

.price-period {
    color: var(--text-secondary) !important;
    font-weight: 500 !important;
}

.plan-features {
    flex-grow: 1;
    margin-bottom: var(--spacing-xl);
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.feature-check {
    color: var(--agri-emerald-accent) !important;
    font-size: 1.25rem !important;
    margin-top: 2px;
    flex-shrink: 0;
}

.feature-text {
    font-size: var(--font-size-caption) !important;
    line-height: 1.5;
    color: var(--text-primary) !important;
}

.limitation-item {
    margin-bottom: var(--spacing-xs);
    padding-left: var(--spacing-md);
}

.limitation-text {
    font-size: var(--font-size-caption) !important;
    color: var(--text-secondary) !important;
    font-style: italic;
}

.plan-button {
    font-size: var(--font-size-h4) !important;
    font-weight: 700 !important;
    padding: var(--spacing-md) var(--spacing-lg) !important;
    border-radius: var(--border-radius-lg) !important;
    text-transform: none !important;
    transition: var(--transition-smooth) !important;
    margin-top: auto;
}

.plan-button-beta {
    background: var(--agri-gradient-primary) !important;
    color: var(--text-light) !important;
    border: none !important;
}

.plan-button-professional {
    background: var(--agri-gradient-secondary) !important;
    color: var(--text-light) !important;
    border: none !important;
}

.plan-button-enterprise {
    background: var(--agri-gradient-enterprise) !important;
    color: var(--text-light) !important;
    border: none !important;
}

.plan-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-card);
}

/* Loading Spinners - AgriIntel Brand Colors */
.MuiCircularProgress-root {
    color: var(--agri-gold-primary) !important;
}

/* Responsive Design - Mobile First Approach */

/* Mobile: 320px-768px */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 var(--spacing-md);
    }

    .nav-actions {
        gap: var(--spacing-sm);
    }

    .nav-button {
        padding: var(--spacing-xs) var(--spacing-md) !important;
        font-size: 0.875rem !important;
    }

    .hero-section {
        padding: var(--spacing-2xl) 0;
        min-height: 90vh;
    }

    .hero-stats {
        justify-content: center;
        gap: var(--spacing-lg);
    }

    .stat-item {
        min-width: 100px;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .cta-primary,
    .cta-secondary {
        width: 100%;
        max-width: 300px;
    }

    .features-grid {
        gap: var(--spacing-lg) !important;
    }

    .feature-card {
        height: 350px;
    }

    .feature-card-content {
        padding: var(--spacing-lg) !important;
    }

    .pricing-grid {
        gap: var(--spacing-lg) !important;
    }

    .pricing-card {
        min-height: 500px;
    }

    .pricing-card-content {
        padding: var(--spacing-lg) !important;
    }
}

/* Tablet: 768px-1024px */
@media (min-width: 768px) and (max-width: 1024px) {
    .hero-grid {
        gap: var(--spacing-xl) !important;
    }

    .hero-stats {
        gap: var(--spacing-xl);
    }

    .features-grid {
        gap: var(--spacing-lg) !important;
    }

    .pricing-grid {
        gap: var(--spacing-lg) !important;
    }

    .pricing-card-professional {
        transform: scale(1.02);
    }
}

/* Desktop: 1024px+ */
@media (min-width: 1024px) {
    .hero-section {
        padding: var(--spacing-3xl) 0;
    }

    .features-section,
    .pricing-section {
        padding: var(--spacing-3xl) 0;
    }
}

/* Large screens: 1440px+ */
@media (min-width: 1440px) {
    .MuiContainer-root {
        max-width: 1400px !important;
    }

    .hero-content {
        padding-right: var(--spacing-xl);
    }

    .feature-card {
        height: 450px;
    }

    .pricing-card {
        min-height: 650px;
    }
}

/* Performance Optimizations */
.agriintel-landing * {
    will-change: transform;
}

.landing-background,
.feature-card-background {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus states for keyboard navigation */
.nav-button:focus,
.cta-primary:focus,
.cta-secondary:focus,
.plan-button:focus {
    outline: 3px solid var(--agri-gold-primary);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .hero-title,
    .hero-subtitle,
    .rating-text {
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    }

    .feature-card,
    .pricing-card {
        border-width: 3px !important;
    }
}

.professional-landing {
    overflow-x: hidden;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Main Content Spacing */
.professional-landing main {
    margin-top: 64px;
}

/* Hero Section */
.hero-section {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: var(--white);
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.8) 0%, rgba(59, 130, 246, 0.8) 100%);
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(13, 27, 42, 0.8), rgba(65, 90, 119, 0.6));
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
}

.hero-title {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.5);
}

.hero-subtitle {
    font-size: 1.4rem;
    font-family: var(--secondary-font);
    margin-bottom: 2.5rem;
    opacity: 0.9;
}

.btn {
    padding: 1rem 2.5rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background-color: var(--cta-color);
    color: var(--text-dark);
    box-shadow: 0 4px 15px rgba(61, 220, 151, 0.4);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(61, 220, 151, 0.6);
}

/* Pricing Section */
.pricing-section {
    padding: 6rem 0;
    background-color: #f7f9fc;
}

.section-title {
    text-align: center;
    font-size: 2.8rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.section-subtitle {
    text-align: center;
    font-size: 1.2rem;
    color: var(--secondary-color);
    margin-bottom: 4rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    align-items: center;
}

.pricing-card {
    background: var(--white);
    border-radius: 15px;
    padding: 2.5rem;
    box-shadow: 0 10px 30px var(--shadow);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 2px solid transparent;
}

.pricing-card.professional {
    transform: scale(1.05);
    border-color: var(--cta-color);
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.tier-name {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.tier-price {
    font-size: 3rem;
    font-weight: 700;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.tier-price span {
    font-size: 1rem;
    font-weight: 400;
}

.tier-features {
    list-style: none;
    padding: 0;
    margin: 2rem 0;
    text-align: left;
}

.tier-features li {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.tier-features li::before {
    content: '✓';
    color: var(--cta-color);
    font-weight: bold;
    margin-right: 10px;
}

.tier-description {
    font-size: 0.9rem;
    color: var(--secondary-color);
    margin-top: 1.5rem;
    min-height: 50px;
}

.btn-secondary {
    background-color: transparent;
    color: var(--cta-color);
    border: 2px solid var(--cta-color);
}

.btn-secondary:hover {
    background-color: var(--cta-color);
    color: var(--text-dark);
}

/* Mobile App Announcement */
.app-announcement {
    background-color: var(--primary-color);
    color: var(--text-light);
    text-align: center;
    padding: 2rem 0;
}

.app-announcement-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.5rem;
}

.app-announcement-icon {
    font-size: 2.5rem;
}

.app-announcement-text {
    font-size: 1.2rem;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 992px) {
    .hero-title {
        font-size: 3rem;
    }
    .pricing-card.professional {
        transform: scale(1);
    }
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    .hero-subtitle {
        font-size: 1.2rem;
    }
    .section-title {
        font-size: 2.2rem;
    }
}
