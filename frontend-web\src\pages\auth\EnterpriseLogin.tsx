import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Container,
  Alert,
  Chip,
  Paper,
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  CheckCircle,
  Star,
  Agriculture,
  HealthAndSafety,
  Restaurant,
  Assessment,
  Support,
  PhoneAndroid,
  TrendingUp,
  Security,
  Analytics,
  Storefront,
  AutoAwesome,
  Groups,
  Business,
  Integration,
  PersonPin,
  School,
  Api,
  Branding
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import AgriIntelBrand from '../../components/branding/AgriIntelBrand';
import '../../styles/auth-pages.css';

const EnterpriseLogin: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const { login } = useAuth();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!username.trim() || !password.trim()) {
      setError('Please enter both username and password');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      await login(username, password);
      // Navigate to enterprise dashboard
      navigate('/dashboard', { replace: true });
    } catch (err: any) {
      setError(err.message || 'Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const enterpriseFeatures = [
    { icon: <Business />, title: 'Custom Integrations', description: 'Tailored solutions for your specific needs' },
    { icon: <PersonPin />, title: 'Dedicated Account Manager', description: 'Personal support and guidance' },
    { icon: <School />, title: 'On-site Training', description: 'Comprehensive training for your team' },
    { icon: <Analytics />, title: 'Custom Reporting', description: 'Bespoke analytics and insights' },
    { icon: <Api />, title: 'API Access', description: 'Full API access for custom development' },
    { icon: <Branding />, title: 'White-label Options', description: 'Brand the platform as your own' }
  ];

  return (
    <Box className="auth-page enterprise-auth" sx={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, rgba(168, 85, 247, 0.1) 0%, rgba(245, 158, 11, 0.1) 100%)',
      display: 'flex',
      alignItems: 'center',
      py: 4
    }}>
      <Container maxWidth="lg">
        <Grid container spacing={4} alignItems="center">
          {/* Login Form */}
          <Grid item xs={12} md={6}>
            <Card sx={{
              maxWidth: 500,
              mx: 'auto',
              boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
              borderRadius: 3,
              overflow: 'visible'
            }}>
              <CardContent sx={{ p: 4 }}>
                <Box sx={{ textAlign: 'center', mb: 4 }}>
                  <AgriIntelBrand variant="compact" size="large" color="primary" />
                  <Chip
                    label="ENTERPRISE"
                    sx={{
                      mt: 2,
                      fontWeight: 600,
                      background: 'linear-gradient(45deg, #A855F7, #7C3AED)',
                      color: 'white'
                    }}
                  />
                </Box>

                <Typography variant="h4" align="center" sx={{ mb: 1, fontWeight: 600 }}>
                  Enterprise Access
                </Typography>
                <Typography variant="body1" align="center" color="text.secondary" sx={{ mb: 4 }}>
                  Custom solutions for large-scale operations with dedicated support and tailored features
                </Typography>

                {error && (
                  <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                  </Alert>
                )}

                <Box component="form" onSubmit={handleLogin}>
                  <TextField
                    fullWidth
                    label="Username"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    margin="normal"
                    required
                    autoFocus
                    placeholder="Enter your enterprise username"
                  />
                  <TextField
                    fullWidth
                    label="Password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    margin="normal"
                    required
                    placeholder="Enter your password"
                  />

                  <Button
                    type="submit"
                    fullWidth
                    variant="contained"
                    size="large"
                    disabled={isLoading}
                    sx={{
                      mt: 3,
                      mb: 2,
                      py: 1.5,
                      background: 'linear-gradient(45deg, #A855F7, #7C3AED)',
                      fontWeight: 600,
                      fontSize: '1.1rem'
                    }}
                  >
                    {isLoading ? 'Signing In...' : 'Access Enterprise Dashboard'}
                  </Button>
                </Box>

                <Box sx={{ textAlign: 'center', mt: 3 }}>
                  <Typography variant="body2" color="text.secondary">
                    Custom pricing - Contact for quote
                  </Typography>
                  <Button
                    variant="text"
                    onClick={() => navigate('/')}
                    sx={{ mt: 1 }}
                  >
                    Back to Home
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Features Overview */}
          <Grid item xs={12} md={6}>
            <Paper sx={{
              p: 4,
              background: 'linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7))',
              backdropFilter: 'blur(10px)',
              borderRadius: 3
            }}>
              <Typography variant="h4" sx={{ mb: 3, fontWeight: 600, color: '#A855F7' }}>
                Enterprise Features
              </Typography>
              <Typography variant="body1" sx={{ mb: 4, color: 'text.secondary' }}>
                Everything in Professional plus custom solutions and dedicated support
              </Typography>

              <List>
                {enterpriseFeatures.map((feature, index) => (
                  <ListItem key={index} sx={{ px: 0, py: 1 }}>
                    <ListItemIcon sx={{ color: '#A855F7' }}>
                      {feature.icon}
                    </ListItemIcon>
                    <ListItemText
                      primary={feature.title}
                      secondary={feature.description}
                      primaryTypographyProps={{ fontWeight: 600 }}
                    />
                  </ListItem>
                ))}
              </List>

              <Box sx={{ mt: 4, p: 3, background: 'rgba(168, 85, 247, 0.1)', borderRadius: 2 }}>
                <Typography variant="h6" sx={{ mb: 1, fontWeight: 600 }}>
                  Custom Solutions
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Tailored integrations, custom modules, and dedicated support for enterprise-scale operations
                </Typography>
                <Button
                  variant="outlined"
                  onClick={() => navigate('/?tab=contact')}
                  sx={{ borderColor: '#A855F7', color: '#A855F7' }}
                >
                  Contact Sales
                </Button>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default EnterpriseLogin;