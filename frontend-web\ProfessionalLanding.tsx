﻿import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Container, Typography, Button, Box } from '@mui/material';
import '../styles/professional-landing.css';

const ProfessionalLanding: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="agriintel-landing">
      <Container maxWidth="xl">
        <Box sx={{ py: 8, textAlign: 'center' }}>
          <Typography variant="h1" sx={{ mb: 4, color: 'white' }}>
             AgriIntel
          </Typography>
          <Typography variant="h3" sx={{ mb: 6, color: 'white' }}>
            Transform Your Livestock Farm with AI Intelligence
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
            <Button
              variant="contained"
              size="large"
              onClick={() => navigate('/login/beta')}
              sx={{ px: 4, py: 2 }}
            >
              Start Free Trial
            </Button>
            <Button
              variant="outlined"
              size="large"
              onClick={() => navigate('/login/professional')}
              sx={{ px: 4, py: 2, color: 'white', borderColor: 'white' }}
            >
              Go Professional
            </Button>
          </Box>
        </Box>
      </Container>
    </div>
  );
};

export default ProfessionalLanding;
